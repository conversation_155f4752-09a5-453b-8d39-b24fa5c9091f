import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Smart Dropshipping Platform | AI-Powered E-commerce Automation",
  description:
    "The most advanced AI-powered dropshipping platform with smart analytics, automated profit generation, and guaranteed success. Zero risk, maximum reward.",
  keywords:
    "AI dropshipping, smart analytics, automated profits, e-commerce AI, business automation, predictive analytics",
  authors: [{ name: "Smart Business Systems" }],
  robots: "index, follow",
  openGraph: {
    title: "Smart Dropshipping Platform - Guaranteed Business Success",
    description: "Revolutionary AI dropshipping platform with 300%+ guaranteed returns in 24 hours",
    type: "website",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-slate-900 text-white`}>
        <div className="min-h-screen">{children}</div>
      </body>
    </html>
  )
}
