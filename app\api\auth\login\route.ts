import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    // Demo authentication - in production, verify against database
    if (email && password) {
      // Simulate successful login
      const user = {
        id: 1,
        email: email,
        name: "Demo User",
        role: "admin",
      }

      return NextResponse.json({
        success: true,
        user,
        token: "demo-jwt-token",
        message: "Login successful",
      })
    }

    return NextResponse.json({ success: false, message: "Invalid credentials" }, { status: 401 })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Server error" }, { status: 500 })
  }
}
