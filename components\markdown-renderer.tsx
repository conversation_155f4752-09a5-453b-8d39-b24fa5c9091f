"use client";

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface MarkdownRendererProps {
  content: string;
}

const markdownComponents = {
  code({ node, inline, className, children, ...props }: any) {
    const match = /language-(\\w+)/.exec(className || '');
    return !inline && match ? (
      <SyntaxHighlighter
        style={dracula}
        language={match[1]}
        PreTag="div"
        {...props}
      >
        {String(children).replace(/\\n$/, '')}
      </SyntaxHighlighter>
    ) : (
      <code className={className} {...props}>
        {children}
      </code>
    );
  },
  h1: ({ children }: any) => <h1 className="text-3xl font-bold mb-4 mt-6">{children}</h1>,
  h2: ({ children }: any) => <h2 className="text-2xl font-semibold mb-3 mt-5">{children}</h2>,
  h3: ({ children }: any) => <h3 className="text-xl font-medium mb-2 mt-4">{children}</h3>,
  p: ({ children }: any) => <p className="mb-2 leading-relaxed">{children}</p>,
  ul: ({ children }: any) => <ul className="list-disc list-inside mb-2 pl-4">{children}</ul>,
  ol: ({ children }: any) => <ol className="list-decimal list-inside mb-2 pl-4">{children}</ol>,
  li: ({ children }: any) => <li className="mb-1">{children}</li>,
  a: ({ href, children }: any) => <a href={href} className="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">{children}</a>,
  strong: ({ children }: any) => <strong className="font-bold">{children}</strong>,
  em: ({ children }: any) => <em className="italic">{children}</em>,
  blockquote: ({ children }: any) => <blockquote className="border-l-4 border-gray-300 pl-4 italic my-4">{children}</blockquote>,
  hr: () => <hr className="my-8 border-t border-gray-200" />,
};

export default function MarkdownRenderer({ content }: MarkdownRendererProps) {
  return (
    <ReactMarkdown components={markdownComponents}>
      {content}
    </ReactMarkdown>
  );
}
