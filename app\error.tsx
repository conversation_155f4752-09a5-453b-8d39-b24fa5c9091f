"use client"

import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Atom, RefreshCw, AlertTriangle } from "lucide-react"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error("Smart Platform Error:", error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <Card className="w-full max-w-md bg-black/40 backdrop-blur-xl border-red-500/30">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <Atom className="h-16 w-16 text-red-400 animate-spin" style={{ animationDuration: "3s" }} />
              <AlertTriangle className="absolute inset-0 h-16 w-16 text-red-500 animate-pulse" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">System Error Detected</CardTitle>
          <CardDescription className="text-gray-400">
            A temporary issue has been detected in the platform
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-300 mb-6">
            Our smart error correction systems are working to resolve this. This is usually fixed within seconds.
          </p>
          <Button
            onClick={reset}
            className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Restart Platform
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
