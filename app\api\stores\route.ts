import { type NextRequest, NextResponse } from "next/server"

export async function GET() {
  try {
    const stores = [
      {
        id: 1,
        name: "TechGadgets Pro",
        platform: "Shopify",
        status: "Active",
        products: 45,
        orders: 128,
        revenue: 15420,
        lastSync: "2 min ago",
        url: "https://techgadgets-pro.myshopify.com",
      },
      {
        id: 2,
        name: "Fitness Essentials",
        platform: "WooCommerce",
        status: "Active",
        products: 32,
        orders: 89,
        revenue: 8950,
        lastSync: "5 min ago",
        url: "https://fitness-essentials.com",
      },
    ]

    return NextResponse.json({
      success: true,
      stores,
      totalStores: stores.length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Failed to fetch stores" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const storeData = await request.json()

    // Simulate store creation
    const newStore = {
      id: Date.now(),
      ...storeData,
      status: "Active",
      products: 0,
      orders: 0,
      revenue: 0,
      lastSync: "Just now",
    }

    return NextResponse.json({
      success: true,
      store: newStore,
      message: "Store created successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Failed to create store" }, { status: 500 })
  }
}
