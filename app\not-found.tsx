import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Atom, Home, AlertTriangle } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <Card className="w-full max-w-md bg-black/40 backdrop-blur-xl border-purple-500/30">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <Atom className="h-16 w-16 text-red-400 animate-spin" style={{ animationDuration: "3s" }} />
              <AlertTriangle className="absolute inset-0 h-16 w-16 text-red-500 animate-pulse" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">Page Not Found</CardTitle>
          <CardDescription className="text-gray-400">
            The page you're looking for doesn't exist in our system
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-300 mb-6">
            Don't worry! Your Smart Dropshipping Platform is operating perfectly. Let's get you back to the business
            dashboard.
          </p>
          <Link href="/">
            <Button className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600">
              <Home className="h-4 w-4 mr-2" />
              Return to Dashboard
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  )
}
