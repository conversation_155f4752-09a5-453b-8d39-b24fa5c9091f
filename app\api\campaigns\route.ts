import { type NextRequest, NextResponse } from "next/server"

export async function GET() {
  try {
    const campaigns = [
      {
        id: 1,
        name: "Smart Fitness Tracker - TikTok",
        platform: "TikTok",
        status: "Active",
        budget: 500,
        spent: 342.5,
        impressions: 45230,
        clicks: 1205,
        conversions: 38,
        roas: 3.2,
        cpc: 0.28,
        ctr: 2.66,
        startDate: "2024-01-10",
        endDate: "2024-01-24",
      },
      {
        id: 2,
        name: "LED Strip Lights - Instagram",
        platform: "Instagram",
        status: "Active",
        budget: 750,
        spent: 623.75,
        impressions: 67890,
        clicks: 2156,
        conversions: 89,
        roas: 4.1,
        cpc: 0.29,
        ctr: 3.18,
        startDate: "2024-01-08",
        endDate: "2024-01-22",
      },
    ]

    return NextResponse.json({
      success: true,
      campaigns,
      totalCampaigns: campaigns.length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Failed to fetch campaigns" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const campaignData = await request.json()

    // Simulate campaign creation
    const newCampaign = {
      id: Date.now(),
      ...campaignData,
      status: "Active",
      spent: 0,
      impressions: 0,
      clicks: 0,
      conversions: 0,
      roas: 0,
      cpc: 0,
      ctr: 0,
    }

    return NextResponse.json({
      success: true,
      campaign: newCampaign,
      message: "Campaign created successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Failed to create campaign" }, { status: 500 })
  }
}
