"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ProductResearch() {
  const [isSearching, setIsSearching] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedPlatform, setSelectedPlatform] = useState("all")

  const products = [
    {
      id: 1,
      name: "Smart Fitness Tracker",
      image: "/placeholder.svg?height=200&width=200",
      price: 29.99,
      cost: 12.5,
      profit: 17.49,
      margin: 58.3,
      viralScore: 87,
      competition: "Medium",
      saturation: "Low",
      platforms: ["TikTok", "Instagram"],
      trend: "Rising",
    },
    {
      id: 2,
      name: "LED Strip Lights",
      image: "/placeholder.svg?height=200&width=200",
      price: 24.99,
      cost: 8.75,
      profit: 16.24,
      margin: 65.0,
      viralScore: 92,
      competition: "High",
      saturation: "Medium",
      platforms: ["TikTok", "YouTube"],
      trend: "Hot",
    },
    {
      id: 3,
      name: "Wireless Earbuds",
      image: "/placeholder.svg?height=200&width=200",
      price: 39.99,
      cost: 15.2,
      profit: 24.79,
      margin: 62.0,
      viralScore: 78,
      competition: "High",
      saturation: "High",
      platforms: ["Instagram", "Facebook"],
      trend: "Stable",
    },
    {
      id: 4,
      name: "Phone Camera Lens Kit",
      image: "/placeholder.svg?height=200&width=200",
      price: 19.99,
      cost: 7.5,
      profit: 12.49,
      margin: 62.5,
      viralScore: 85,
      competition: "Low",
      saturation: "Low",
      platforms: ["TikTok", "Instagram", "YouTube"],
      trend: "Rising",
    },
  ]

  const handleSearch = () => {
    setIsSearching(true)
    setTimeout(() => {
      setIsSearching(false)
    }, 2000)
  }

  const getViralScoreColor = (score: number) => {
    if (score >= 85) return "bg-green-500"
    if (score >= 70) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "Hot":
        return "🔥"
      case "Rising":
        return "📈"
      case "Stable":
        return "📊"
      default:
        return "📉"
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Product Research</h1>
        <p className="text-gray-600">AI-powered product discovery and analysis</p>
      </div>

      {/* Search Controls */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>AI Product Discovery</CardTitle>
          <CardDescription>Find trending products with high profit potential</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Enter keywords, niche, or let AI suggest..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="tiktok">TikTok</SelectItem>
                <SelectItem value="instagram">Instagram</SelectItem>
                <SelectItem value="facebook">Facebook</SelectItem>
                <SelectItem value="youtube">YouTube</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch} disabled={isSearching} className="w-full md:w-auto">
              {isSearching ? "🤖 AI Searching..." : "🔍 Find Products"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="aspect-square bg-gray-100 relative">
              <img
                src={product.image || "/placeholder.svg"}
                alt={product.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-2 right-2">
                <Badge className={`${getViralScoreColor(product.viralScore)} text-white`}>
                  Viral: {product.viralScore}
                </Badge>
              </div>
              <div className="absolute top-2 left-2">
                <Badge variant="outline" className="bg-white">
                  {getTrendIcon(product.trend)} {product.trend}
                </Badge>
              </div>
            </div>

            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2">{product.name}</h3>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Sell Price:</span>
                  <span className="font-semibold">${product.price}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cost:</span>
                  <span>${product.cost}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Profit:</span>
                  <span className="font-semibold text-green-600">${product.profit}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Margin:</span>
                  <span className="font-semibold">{product.margin}%</span>
                </div>
              </div>

              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Competition:</span>
                  <Badge
                    variant={
                      product.competition === "Low"
                        ? "default"
                        : product.competition === "Medium"
                          ? "secondary"
                          : "destructive"
                    }
                  >
                    {product.competition}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Saturation:</span>
                  <Badge
                    variant={
                      product.saturation === "Low"
                        ? "default"
                        : product.saturation === "Medium"
                          ? "secondary"
                          : "destructive"
                    }
                  >
                    {product.saturation}
                  </Badge>
                </div>
              </div>

              <div className="mt-4">
                <p className="text-xs text-gray-600 mb-2">Trending on:</p>
                <div className="flex flex-wrap gap-1">
                  {product.platforms.map((platform) => (
                    <Badge key={platform} variant="outline" className="text-xs">
                      {platform}
                    </Badge>
                  ))}
                </div>
              </div>

              <Button className="w-full mt-4" size="sm">
                Add to Store
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
