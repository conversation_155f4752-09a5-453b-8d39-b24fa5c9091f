"use client"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function StoreManager() {
  const stores = [
    {
      id: 1,
      name: "TechGadgets Pro",
      platform: "Shopify",
      status: "Active",
      products: 45,
      orders: 128,
      revenue: 15420,
      lastSync: "2 min ago",
    },
    {
      id: 2,
      name: "Fitness Essentials",
      platform: "WooCommerce",
      status: "Active",
      products: 32,
      orders: 89,
      revenue: 8950,
      lastSync: "5 min ago",
    },
    {
      id: 3,
      name: "Home & Living",
      platform: "Shopify",
      status: "Paused",
      products: 67,
      orders: 45,
      revenue: 3200,
      lastSync: "1 hour ago",
    },
  ]

  const products = [
    {
      id: 1,
      name: "Smart Fitness Tracker",
      sku: "SFT-001",
      price: 29.99,
      stock: 150,
      sold: 45,
      status: "Active",
      store: "Fitness Essentials",
    },
    {
      id: 2,
      name: "LED Strip Lights",
      sku: "LED-002",
      price: 24.99,
      stock: 8,
      sold: 92,
      status: "Low Stock",
      store: "TechGadgets Pro",
    },
    {
      id: 3,
      name: "Wireless Earbuds",
      sku: "WE-003",
      price: 39.99,
      stock: 0,
      sold: 156,
      status: "Out of Stock",
      store: "TechGadgets Pro",
    },
    {
      id: 4,
      name: "Phone Camera Lens",
      sku: "PCL-004",
      price: 19.99,
      stock: 75,
      sold: 23,
      status: "Active",
      store: "TechGadgets Pro",
    },
  ]

  const orders = [
    {
      id: "#ORD-001",
      customer: "John Smith",
      products: 2,
      total: 59.98,
      status: "Processing",
      date: "2024-01-15",
      store: "TechGadgets Pro",
    },
    {
      id: "#ORD-002",
      customer: "Sarah Johnson",
      products: 1,
      total: 29.99,
      status: "Shipped",
      date: "2024-01-15",
      store: "Fitness Essentials",
    },
    {
      id: "#ORD-003",
      customer: "Mike Wilson",
      products: 3,
      total: 89.97,
      status: "Delivered",
      date: "2024-01-14",
      store: "TechGadgets Pro",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      case "low stock":
        return "bg-orange-100 text-orange-800"
      case "out of stock":
        return "bg-red-100 text-red-800"
      case "processing":
        return "bg-blue-100 text-blue-800"
      case "shipped":
        return "bg-purple-100 text-purple-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Store Manager</h1>
        <p className="text-gray-600">Manage your stores, products, and orders</p>
      </div>

      {/* Store Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {stores.map((store) => (
          <Card key={store.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{store.name}</CardTitle>
                  <CardDescription>{store.platform}</CardDescription>
                </div>
                <Badge className={getStatusColor(store.status)}>{store.status}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Products:</span>
                  <span className="font-semibold">{store.products}</span>
                </div>
                <div className="flex justify-between">
                  <span>Orders:</span>
                  <span className="font-semibold">{store.orders}</span>
                </div>
                <div className="flex justify-between">
                  <span>Revenue:</span>
                  <span className="font-semibold">${store.revenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Last sync:</span>
                  <span>{store.lastSync}</span>
                </div>
              </div>
              <Button className="w-full mt-4" size="sm">
                Manage Store
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Management Tabs */}
      <Tabs defaultValue="products" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Product Inventory</CardTitle>
              <CardDescription>Manage your product catalog and inventory</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Sold</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Store</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.sku}</TableCell>
                      <TableCell>${product.price}</TableCell>
                      <TableCell>{product.stock}</TableCell>
                      <TableCell>{product.sold}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(product.status)}>{product.status}</Badge>
                      </TableCell>
                      <TableCell>{product.store}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Order Management</CardTitle>
              <CardDescription>Track and manage customer orders</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Products</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Store</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.id}</TableCell>
                      <TableCell>{order.customer}</TableCell>
                      <TableCell>{order.products}</TableCell>
                      <TableCell>${order.total}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(order.status)}>{order.status}</Badge>
                      </TableCell>
                      <TableCell>{order.date}</TableCell>
                      <TableCell>{order.store}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Store Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Revenue</span>
                    <span className="font-bold">$27,570</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Orders</span>
                    <span className="font-bold">262</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Order Value</span>
                    <span className="font-bold">$105.23</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Conversion Rate</span>
                    <span className="font-bold">3.2%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Wireless Earbuds</span>
                    <span className="font-bold">156 sold</span>
                  </div>
                  <div className="flex justify-between">
                    <span>LED Strip Lights</span>
                    <span className="font-bold">92 sold</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Smart Fitness Tracker</span>
                    <span className="font-bold">45 sold</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Phone Camera Lens</span>
                    <span className="font-bold">23 sold</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Store Settings</CardTitle>
              <CardDescription>Configure your store preferences and integrations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Platform Integrations</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <span className="font-medium">Shopify</span>
                        <p className="text-sm text-gray-600">2 stores connected</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <span className="font-medium">WooCommerce</span>
                        <p className="text-sm text-gray-600">1 store connected</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <span className="font-medium">BigCommerce</span>
                        <p className="text-sm text-gray-600">Not connected</p>
                      </div>
                      <Button size="sm" variant="outline">
                        Connect
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">Automation Settings</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Auto-sync inventory</span>
                      <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Price optimization</span>
                      <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Order notifications</span>
                      <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
