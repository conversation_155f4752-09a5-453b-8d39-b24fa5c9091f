# Dropshipping AI Platform - Complete Local Setup Guide

## 🚀 Quick Start (5 Minutes)

### Prerequisites
- Node.js 18+ installed
- Git installed
- Code editor (VS Code recommended)

### Step 1: Extract and Setup
```bash
# Extract the downloaded zip file
unzip dropshipping-ai-platform.zip
cd dropshipping-ai-platform

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend/dropshipping-dashboard
npm install
```

### Step 2: Start the Application
```bash
# Terminal 1 - Start Backend (from backend folder)
cd backend
npm run dev

# Terminal 2 - Start Frontend (from frontend/dropshipping-dashboard folder)
cd frontend/dropshipping-dashboard
npm run dev
```

### Step 3: Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3000
- **Demo Login**: The app will automatically log you in with demo data

## 📁 Project Structure

```
dropshipping-ai-platform/
├── backend/                          # Node.js/Express API
│   ├── src/
│   │   ├── routes/                   # API endpoints
│   │   ├── services/                 # AI automation engines
│   │   ├── middleware/               # Authentication & security
│   │   └── server.js                 # Main server file
│   ├── prisma/                       # Database schema
│   ├── package.json                  # Dependencies
│   └── .env.example                  # Environment template
├── frontend/dropshipping-dashboard/  # React application
│   ├── src/
│   │   ├── components/               # UI components
│   │   ├── hooks/                    # React hooks
│   │   ├── lib/                      # API client
│   │   └── App.jsx                   # Main app
│   ├── package.json                  # Dependencies
│   └── .env.example                  # Environment template
├── docs/                             # Documentation
├── SETUP_GUIDE.md                    # This file
└── README.md                         # Project overview
```

## 🔧 Detailed Setup Instructions

### Backend Setup

1. **Navigate to backend folder**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration (Optional)**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env file with your API keys (optional for demo)
   # The app works with demo data without API keys
   ```

4. **Start the backend server**
   ```bash
   npm run dev
   ```
   
   The backend will start on http://localhost:3000

### Frontend Setup

1. **Navigate to frontend folder**
   ```bash
   cd frontend/dropshipping-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the frontend application**
   ```bash
   npm run dev
   ```
   
   The frontend will start on http://localhost:5173

## 🎯 Features Overview

### 1. Dashboard
- Real-time analytics and metrics
- AI automation status monitoring
- Revenue and performance tracking
- Recent activity feed

### 2. Product Research
- AI-powered product discovery
- Viral score calculation (1-100)
- Multi-platform scanning simulation
- Profit margin analysis

### 3. Store Management
- Multi-store support
- Product inventory management
- Order tracking
- Performance analytics

### 4. Campaign Management
- Multi-platform advertising
- AI-powered targeting
- Real-time optimization
- ROI tracking

### 5. Analytics
- Revenue analytics
- Product performance
- Campaign metrics
- Business insights

## 🤖 AI Features (Demo Mode)

The application includes simulated AI features that demonstrate the full functionality:

- **Product Research Engine**: Finds trending products with viral scores
- **Price Optimization**: Dynamic pricing algorithms
- **Content Generation**: AI-generated product descriptions
- **Marketing Automation**: Smart campaign management
- **Analytics Engine**: Performance insights and recommendations

## 🔑 API Keys (Optional)

For full functionality, you can add these API keys to the `.env` files:

### Backend (.env)
```env
# AI Services
OPENAI_API_KEY=your_openai_api_key

# Social Media APIs
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
GOOGLE_ADS_CLIENT_ID=your_google_client_id
GOOGLE_ADS_CLIENT_SECRET=your_google_client_secret

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### Frontend (.env)
```env
VITE_API_URL=http://localhost:3000/api
VITE_APP_NAME=Dropshipping AI Platform
```

## 🐳 Docker Setup (Alternative)

If you prefer Docker:

1. **Install Docker and Docker Compose**

2. **Start with Docker**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:5173
   - Backend: http://localhost:3000

## 🔧 Troubleshooting

### Common Issues

**Port already in use:**
```bash
# Kill processes on ports 3000 and 5173
npx kill-port 3000
npx kill-port 5173
```

**Dependencies not installing:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules
npm install
```

**Frontend not connecting to backend:**
- Ensure backend is running on port 3000
- Check that VITE_API_URL in frontend/.env points to http://localhost:3000/api

### Development Tips

1. **Hot Reload**: Both frontend and backend support hot reload
2. **API Testing**: Use http://localhost:3000/api/health to test backend
3. **Browser DevTools**: Use React DevTools for debugging
4. **Network Tab**: Monitor API calls in browser DevTools

## 📚 Available Scripts

### Backend Scripts
```bash
npm run dev          # Start development server
npm start           # Start production server
npm run test        # Run tests
```

### Frontend Scripts
```bash
npm run dev         # Start development server
npm run build       # Build for production
npm run preview     # Preview production build
npm run test        # Run tests
```

## 🌐 Production Deployment

### Quick Deployment Options

1. **Vercel (Frontend) + Railway (Backend)**
   - Deploy frontend to Vercel
   - Deploy backend to Railway
   - Update API URL in frontend environment

2. **Netlify (Frontend) + Heroku (Backend)**
   - Deploy frontend to Netlify
   - Deploy backend to Heroku
   - Configure environment variables

3. **AWS/DigitalOcean**
   - Use provided Docker configuration
   - Follow deployment guide in docs/

## 🎨 Customization

### Branding
- Update logo in `frontend/src/assets/`
- Modify colors in `frontend/src/index.css`
- Change app name in environment variables

### Features
- Add new API routes in `backend/src/routes/`
- Create new React components in `frontend/src/components/`
- Extend AI services in `backend/src/services/`

## 📖 Documentation

- **API Documentation**: `docs/API.md`
- **User Manual**: `docs/USER_MANUAL.md`
- **Deployment Guide**: `docs/DEPLOYMENT.md`
- **Architecture**: `docs/ARCHITECTURE.md`

## 🆘 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Review the documentation in the `docs/` folder
3. Check browser console for error messages
4. Ensure all dependencies are properly installed

## 🎉 Success!

Once both servers are running, you'll have:
- A fully functional dropshipping AI platform
- Professional dashboard with real-time data
- AI-powered product research tools
- Campaign management system
- Comprehensive analytics

The application includes demo data and simulated AI responses, so you can explore all features immediately without requiring external API keys.

Enjoy building your dropshipping empire with AI! 🚀

