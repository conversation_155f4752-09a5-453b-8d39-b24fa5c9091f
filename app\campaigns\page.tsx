"use client"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export default function CampaignManager() {
  const campaigns = [
    {
      id: 1,
      name: "Smart Fitness Tracker - TikTok",
      platform: "TikTok",
      status: "Active",
      budget: 500,
      spent: 342.5,
      impressions: 45230,
      clicks: 1205,
      conversions: 38,
      roas: 3.2,
      cpc: 0.28,
      ctr: 2.66,
      startDate: "2024-01-10",
      endDate: "2024-01-24",
    },
    {
      id: 2,
      name: "LED Strip Lights - Instagram",
      platform: "Instagram",
      status: "Active",
      budget: 750,
      spent: 623.75,
      impressions: 67890,
      clicks: 2156,
      conversions: 89,
      roas: 4.1,
      cpc: 0.29,
      ctr: 3.18,
      startDate: "2024-01-08",
      endDate: "2024-01-22",
    },
    {
      id: 3,
      name: "Wireless Earbuds - Facebook",
      platform: "Facebook",
      status: "Paused",
      budget: 400,
      spent: 156.8,
      impressions: 23450,
      clicks: 567,
      conversions: 12,
      roas: 1.8,
      cpc: 0.28,
      ctr: 2.42,
      startDate: "2024-01-12",
      endDate: "2024-01-26",
    },
    {
      id: 4,
      name: "Phone Camera Lens - YouTube",
      platform: "YouTube",
      status: "Active",
      budget: 600,
      spent: 234.6,
      impressions: 34560,
      clicks: 892,
      conversions: 23,
      roas: 2.9,
      cpc: 0.26,
      ctr: 2.58,
      startDate: "2024-01-14",
      endDate: "2024-01-28",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      case "completed":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case "tiktok":
        return "🎵"
      case "instagram":
        return "📸"
      case "facebook":
        return "👥"
      case "youtube":
        return "📺"
      default:
        return "📱"
    }
  }

  const getRoasColor = (roas: number) => {
    if (roas >= 3.0) return "text-green-600"
    if (roas >= 2.0) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Campaign Manager</h1>
        <p className="text-gray-600">AI-powered advertising campaign management</p>
      </div>

      {/* Campaign Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spend</CardTitle>
            <span className="text-2xl">💰</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1,357</div>
            <p className="text-xs text-muted-foreground">+12% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
            <span className="text-2xl">🎯</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">162</div>
            <p className="text-xs text-muted-foreground">+8% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average ROAS</CardTitle>
            <span className="text-2xl">📈</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.0x</div>
            <p className="text-xs text-muted-foreground">+0.2x from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <span className="text-2xl">🚀</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">1 paused</p>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>🤖 AI Campaign Insights</CardTitle>
          <CardDescription>Real-time optimization recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
              <span className="text-green-600">✅</span>
              <div>
                <p className="font-medium text-green-800">LED Strip Lights campaign performing well</p>
                <p className="text-sm text-green-600">
                  ROAS of 4.1x exceeds target. Consider increasing budget by 20%.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
              <span className="text-yellow-600">⚠️</span>
              <div>
                <p className="font-medium text-yellow-800">Wireless Earbuds campaign needs attention</p>
                <p className="text-sm text-yellow-600">
                  ROAS of 1.8x below target. Recommend pausing or adjusting targeting.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
              <span className="text-blue-600">💡</span>
              <div>
                <p className="font-medium text-blue-800">New opportunity detected</p>
                <p className="text-sm text-blue-600">
                  Smart Fitness Tracker showing strong engagement on Instagram. Consider cross-platform expansion.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaign Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {campaigns.map((campaign) => (
          <Card key={campaign.id} className="overflow-hidden">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    {getPlatformIcon(campaign.platform)}
                    {campaign.name}
                  </CardTitle>
                  <CardDescription>
                    {campaign.platform} • {campaign.startDate} - {campaign.endDate}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(campaign.status)}>{campaign.status}</Badge>
                  <Button size="sm" variant="outline">
                    {campaign.status === "Active" ? "⏸️" : "▶️"}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Budget Progress */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Budget Used</span>
                  <span>
                    ${campaign.spent} / ${campaign.budget}
                  </span>
                </div>
                <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2" />
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Impressions:</span>
                    <span className="font-semibold">{campaign.impressions.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Clicks:</span>
                    <span className="font-semibold">{campaign.clicks.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Conversions:</span>
                    <span className="font-semibold">{campaign.conversions}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">ROAS:</span>
                    <span className={`font-semibold ${getRoasColor(campaign.roas)}`}>{campaign.roas}x</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">CPC:</span>
                    <span className="font-semibold">${campaign.cpc}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">CTR:</span>
                    <span className="font-semibold">{campaign.ctr}%</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button size="sm" variant="outline" className="flex-1">
                  📊 Analytics
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  ⚙️ Edit
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  🤖 Optimize
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create New Campaign */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Create New Campaign</CardTitle>
          <CardDescription>Launch AI-optimized advertising campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="h-20 flex flex-col space-y-2">
              <span className="text-2xl">🎵</span>
              <span className="text-sm">TikTok Ads</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <span className="text-2xl">📸</span>
              <span className="text-sm">Instagram Ads</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <span className="text-2xl">👥</span>
              <span className="text-sm">Facebook Ads</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <span className="text-2xl">📺</span>
              <span className="text-sm">YouTube Ads</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
