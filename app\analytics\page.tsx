"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export default function Analytics() {
  const revenueData = [
    { month: "Jan", revenue: 12500, orders: 89 },
    { month: "Feb", revenue: 18750, orders: 134 },
    { month: "Mar", revenue: 24300, orders: 178 },
    { month: "Apr", revenue: 31200, orders: 223 },
    { month: "May", revenue: 28900, orders: 201 },
    { month: "Jun", revenue: 35600, orders: 267 },
  ]

  const topProducts = [
    { name: "Wireless Earbuds", revenue: 8950, units: 156, growth: 23.5 },
    { name: "LED Strip Lights", revenue: 6780, units: 92, growth: 18.2 },
    { name: "Smart Fitness Tracker", revenue: 5420, units: 45, growth: 15.8 },
    { name: "Phone Camera Lens", revenue: 3210, units: 23, growth: 12.1 },
  ]

  const trafficSources = [
    { source: "TikTok", visitors: 12450, percentage: 35.2, conversions: 445 },
    { source: "Instagram", visitors: 9870, percentage: 27.9, conversions: 356 },
    { source: "Facebook", visitors: 7650, percentage: 21.6, conversions: 278 },
    { source: "YouTube", visitors: 4320, percentage: 12.2, conversions: 156 },
    { source: "Direct", visitors: 1110, percentage: 3.1, conversions: 45 },
  ]

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p className="text-gray-600">Comprehensive business intelligence and insights</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <span className="text-2xl">💰</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$151,250</div>
            <p className="text-xs text-muted-foreground">+24.5% from last period</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <span className="text-2xl">📦</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,092</div>
            <p className="text-xs text-muted-foreground">+18.2% from last period</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <span className="text-2xl">📈</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.7%</div>
            <p className="text-xs text-muted-foreground">+0.5% from last period</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
            <span className="text-2xl">💎</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$138.46</div>
            <p className="text-xs text-muted-foreground">+5.2% from last period</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Revenue Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Monthly revenue and order growth</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {revenueData.map((data, index) => (
                <div key={data.month} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium w-8">{data.month}</span>
                    <div className="flex-1">
                      <Progress value={(data.revenue / 35600) * 100} className="h-2 w-32" />
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold">${data.revenue.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">{data.orders} orders</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Products</CardTitle>
            <CardDescription>Best sellers by revenue and growth</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-bold text-blue-600">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.units} units sold</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${product.revenue.toLocaleString()}</div>
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      +{product.growth}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Traffic Sources */}
        <Card>
          <CardHeader>
            <CardTitle>Traffic Sources</CardTitle>
            <CardDescription>Where your customers are coming from</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trafficSources.map((source) => (
                <div key={source.source} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{source.source}</span>
                    <span className="text-sm text-gray-600">{source.percentage}%</span>
                  </div>
                  <Progress value={source.percentage} className="h-2" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{source.visitors.toLocaleString()} visitors</span>
                    <span>{source.conversions} conversions</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle>🤖 AI Performance Insights</CardTitle>
            <CardDescription>Automated analysis and recommendations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <span className="text-green-600">✅</span>
                  <div>
                    <p className="font-medium text-green-800">Strong Performance</p>
                    <p className="text-sm text-green-600">
                      Revenue up 24.5% with healthy conversion rates across all channels.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600">💡</span>
                  <div>
                    <p className="font-medium text-blue-800">Optimization Opportunity</p>
                    <p className="text-sm text-blue-600">
                      TikTok traffic has highest conversion rate. Consider increasing ad spend by 15%.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <span className="text-yellow-600">⚠️</span>
                  <div>
                    <p className="font-medium text-yellow-800">Inventory Alert</p>
                    <p className="text-sm text-yellow-600">
                      Top performing products may run low. Consider restocking within 7 days.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-purple-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <span className="text-purple-600">🎯</span>
                  <div>
                    <p className="font-medium text-purple-800">New Trend Detected</p>
                    <p className="text-sm text-purple-600">
                      Phone accessories showing 45% growth. Explore similar products.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
