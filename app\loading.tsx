import { <PERSON>, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>, C<PERSON> } from "lucide-react"

export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <Card className="w-full max-w-md bg-black/40 backdrop-blur-xl border-purple-500/30">
        <CardContent className="pt-6">
          <div className="text-center space-y-6">
            <div className="flex justify-center space-x-4">
              <Atom className="h-12 w-12 text-cyan-400 animate-spin" style={{ animationDuration: "3s" }} />
              <Brain className="h-12 w-12 text-purple-400 animate-pulse" />
              <Cpu className="h-12 w-12 text-blue-400 animate-bounce" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">Starting Smart Platform...</h2>
              <p className="text-gray-400">AI systems coming online</p>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">AI Systems</span>
                <span className="text-green-400">97.3%</span>
              </div>
              <Progress value={97.3} className="h-2 bg-gray-800" />
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Smart Analytics</span>
                <span className="text-blue-400">94.8%</span>
              </div>
              <Progress value={94.8} className="h-2 bg-gray-800" />
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Business Tools</span>
                <span className="text-purple-400">99.2%</span>
              </div>
              <Progress value={99.2} className="h-2 bg-gray-800" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
