import { NextResponse } from "next/server"

export async function GET() {
  try {
    const analytics = {
      revenue: {
        total: 151250,
        growth: 24.5,
        monthly: [
          { month: "Jan", revenue: 12500, orders: 89 },
          { month: "Feb", revenue: 18750, orders: 134 },
          { month: "Mar", revenue: 24300, orders: 178 },
          { month: "Apr", revenue: 31200, orders: 223 },
          { month: "May", revenue: 28900, orders: 201 },
          { month: "Jun", revenue: 35600, orders: 267 },
        ],
      },
      orders: {
        total: 1092,
        growth: 18.2,
      },
      conversion: {
        rate: 3.7,
        growth: 0.5,
      },
      avgOrderValue: {
        value: 138.46,
        growth: 5.2,
      },
      topProducts: [
        { name: "Wireless Earbuds", revenue: 8950, units: 156, growth: 23.5 },
        { name: "LED Strip Lights", revenue: 6780, units: 92, growth: 18.2 },
        { name: "Smart Fitness Tracker", revenue: 5420, units: 45, growth: 15.8 },
        { name: "Phone Camera Lens", revenue: 3210, units: 23, growth: 12.1 },
      ],
      trafficSources: [
        { source: "TikTok", visitors: 12450, percentage: 35.2, conversions: 445 },
        { source: "Instagram", visitors: 9870, percentage: 27.9, conversions: 356 },
        { source: "Facebook", visitors: 7650, percentage: 21.6, conversions: 278 },
        { source: "YouTube", visitors: 4320, percentage: 12.2, conversions: 156 },
        { source: "Direct", visitors: 1110, percentage: 3.1, conversions: 45 },
      ],
    }

    return NextResponse.json({
      success: true,
      analytics,
    })
  } catch (error) {
    return NextResponse.json({ success: false, message: "Failed to fetch analytics" }, { status: 500 })
  }
}
